<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('rg_locations_monthly_score', function (Blueprint $table) {
            $table->id();
            $table->integer('organisation_location_id')->unsigned();
            $table->foreign('organisation_location_id')->references('id')->on('organisation_locations');

            // month and year this score is applicable
            $table->date('coverage');

            $table->integer('score')->unsigned();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('rg_locations_monthly_score');
    }
};
