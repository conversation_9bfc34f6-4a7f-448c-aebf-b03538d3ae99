<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('sub_sectors', function (Blueprint $table) {
            $table->id();
            $table->unsignedInteger('sector_id')->foreign('sector_id')->references('id')->on('sectors');
            $table->string('handle');
            $table->string('type');
            $table->timestamps();
        });

        Schema::table('organisations', function (Blueprint $table) {
            $table->foreignId('sub_sector_id')->nullable()->constrained('sub_sectors');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('sub_sectors');
    }
};
