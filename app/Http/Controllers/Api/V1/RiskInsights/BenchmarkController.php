<?php

namespace App\Http\Controllers\Api\V1\RiskInsights;

use App\Http\Controllers\Controller;
use App\Models\Organisation;
use App\Models\OrganisationLocations;
use Carbon\Carbon;
use App\Models\RiskInsights\LocationMonthlyScore;
use Illuminate\Http\Request;

class BenchmarkController extends Controller
{
    /**
     * Mapping of attribute names to their abbreviations used in the JSON response
     */
    private const ATTRIBUTE_MAPPING = [
        'construction and exposures' => 'c_e',
        'occupancy hazards and process safety' => 'oh',
        'management safe systems of work' => 'SSoW',
        'fire detection and protection' => 'fd_p',
        'security' => 'security',
        'utilities' => 'utilities',
        'special perils' => 'sp',
        'financial business risk exposure' => 'fr',
    ];

    public function index(Request $request)
    {
        $benchmarking = Organisation::with([
                'broker:id,name,image',
                'leadInsurer:id,name,image',
                'sector:id,handle,type as sector_data',
                'subSector:id,handle,type',
                'surveys:id,organisation_id',
            ])
            ->whereNotNull('risk_score')
            ->select([
                'id',
                'name',
                'logo',
                'premium',
                'position',
                'line_size',
                'expiry_date_of_cover',
                'underwriter',
                'risk_score',
                'policy_segment',
                'broker_id',
                'sector',
                'sub_sector_id',
                'lead_insurer_id',
                'policy_segment',
                'underwriter',
                'updated_at',
        ]);

        if ($request->has('sector_id')) {
            $benchmarking->where('sector', $request->sector_id);
        }

        if ($request->has('sub_sector_id')) {
            $benchmarking->where('sub_sector_id', $request->sub_sector_id);
        }

        $benchmarking = $benchmarking->get();

        return response()->json([
            'response' => 'success',
            'data'     => $benchmarking,
        ]);
    }

    public function organisation()
    {
        // $organisationJson = file_get_contents(public_path('templates-league/data/default/organisation_benchmarking.json'));
        // return response()->json(json_decode($organisationJson, true));

        // Get all organization locations with their related data
        $locations = OrganisationLocations::where('organisation_id', '>=', 3917)->with([
            'organisation',
            'standardLocationsGradingsOverview' => function ($query) {
                $query->where('attribute_type', 'attribute');
            }
        ])->get();

        $formattedLocations = [];

        foreach ($locations as $location) {
            $locationData = [
                'location' => $location->location_name,
                'postcode' => $location->postcode,
                'tiv' => $location->tiv ? (string) $location->tiv : "5000000",
                'premium' => $location->organisation->premium ?? "94021",
                'segment' => $this->getSegmentForLocation($location),
                'tco' => $this->getTcoForLocation($location),
            ];

            // Get attribute scores and map them to abbreviations
            $attributeScores = $this->getAttributeScores($location);
            foreach (self::ATTRIBUTE_MAPPING as $fullName => $abbreviation) {
                $locationData[$abbreviation] = $attributeScores[$fullName] ?? "0";
            }

            // Get monthly risk scores for chart data
            $riskScoreData = $this->getRiskScoreData($location);
            $locationData['risk_score'] = $riskScoreData;

            $formattedLocations[] = $locationData;
        }

        return response()->json([
            'locations' => $formattedLocations
        ]);
    }

    /**
     * Get attribute scores for a location
     */
    private function getAttributeScores(OrganisationLocations $location): array
    {
        $scores = [];

        foreach ($location->standardLocationsGradingsOverview as $grading) {
            $attributeName = strtolower($grading->attribute);
            if (isset(self::ATTRIBUTE_MAPPING[$attributeName])) {
                // Convert score to percentage if it's a decimal
                $score = $grading->value;
                if (is_numeric($score) && $score <= 1) {
                    $score = (int)($score * 100);
                }
                $scores[$attributeName] = (string) $score;
            }
        }

        return $scores;
    }

    /**
     * Get monthly risk score data for chart
     */
    private function getRiskScoreData(OrganisationLocations $location): array
    {
        $chartData = [];
        $latestScore = 50; // Default value

        try {
            $monthlyScores = LocationMonthlyScore::where('organisation_location_id', $location->id)
                ->orderBy('coverage', 'asc')
                ->get();

            if ($monthlyScores->isNotEmpty()) {
                foreach ($monthlyScores as $score) {
                    $month = date('M', strtotime($score->coverage));
                    $chartData[] = [
                        'month' => $month,
                        'value' => (int) $score->score
                    ];
                    $latestScore = (int) $score->score;
                }
            } else {
                $chartData = $this->generateDefaultChartData();
                $latestScore = $chartData[count($chartData) - 1]['value'];
            }
        } catch (\Exception $e) {
            // If table doesn't exist or there's an error, use default data
            $chartData = $this->generateDefaultChartData();
            $latestScore = $chartData[count($chartData) - 1]['value'];
        }

        return [
            'chart_data' => $chartData,
            'risk_league_rating' => [
                'value' => $latestScore
            ]
        ];
    }

    /**
     * Format organisation data for benchmark response
     */
    private function formatOrganisationForBenchmark(\App\Models\Organisation $organisation): array
    {
        // Generate slug from name
        $slug = strtolower(str_replace([' ', '&'], ['', '&'], $organisation->name));

        // Get broker name
        $brokerName = $organisation->broker ? $organisation->broker->name : '-';

        // Count surveys
        $surveyCount = $organisation->surveys->count();

        // Format last updated date
        $lastUpdated = $organisation->updated_at ? $organisation->updated_at->format('d/m/y') : '-';

        // Get risk score data
        $riskScoreData = $this->getOrganisationRiskScore($organisation);

        return [
            'id' => $organisation->id,
            'name' => $organisation->name,
            'slug' => $slug,
            'sector' => $organisation->product_sector ?? 'Unknown',
            'sub_sector' => $organisation->product_subsector ?? 'Unknown',
            'broker' => $brokerName,
            'position' => $organisation->position ?? '-', 
            'lead_insurer' => '-', // Placeholder - may need to be mapped from another field
            'line_size' => $organisation->line_size ?? '-',
            'surveys' => $surveyCount,
            'last_updated' => $lastUpdated,
            'policy_segment' => '-', // Placeholder - may need to be mapped from another field
            'renewal_date' => $organisation->expiry_date_of_cover ?
                \Carbon\Carbon::parse($organisation->expiry_date_of_cover)->format('d/m/y') : '-',
            'underwriter' => '-', // Placeholder - may need to be mapped from another field
            'risk_score' => $riskScoreData
        ];
    }

    /**
     * Get risk score data for organisation
     */
    private function getOrganisationRiskScore(\App\Models\Organisation $organisation): array
    {
        $chartData = [];
        $latestScore = 50; // Default value
        $seriesColor = $this->getSeriesColorByScore($latestScore);

        // Try to get monthly scores from organisation locations
        $monthlyScores = [];
        foreach ($organisation->locations as $location) {
            if (isset($location->monthlyScores)) {
                foreach ($location->monthlyScores as $score) {
                    $month = date('M', strtotime($score->coverage));
                    $monthlyScores[$month] = (int) $score->score;
                }
            }
        }

        if (!empty($monthlyScores)) {
            $months = ['Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec', 'Jan', 'Feb', 'Mar'];
            foreach ($months as $month) {
                $chartData[] = [
                    'month' => $month,
                    'value' => $monthlyScores[$month] ?? rand(30, 70)
                ];
            }
            $latestScore = end($chartData)['value'];
        } else {
            $chartData = $this->generateDefaultChartData();
            $latestScore = end($chartData)['value'];
        }

        $seriesColor = $this->getSeriesColorByScore($latestScore);

        return [
            'series_color' => $seriesColor,
            'chart_data' => $chartData,
            'risk_league_rating' => [
                'value' => $latestScore
            ]
        ];
    }

    /**
     * Generate default chart data for demo purposes
     */
    private function generateDefaultChartData(): array
    {
        $months = ['Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec', 'Jan', 'Feb', 'Mar'];
        $chartData = [];

        foreach ($months as $month) {
            $chartData[] = [
                'month' => $month,
                'value' => rand(20, 80) // Random values for demo
            ];
        }

        return $chartData;
    }

    /**
     * Get series color based on risk score
     */
    private function getSeriesColorByScore(int $score): string
    {
        if ($score >= 80) {
            return '49B2C9'; // Blue for high scores
        } elseif ($score >= 60) {
            return '49C993'; // Green for medium-high scores
        } else {
            return 'FDCA41'; // Yellow for lower scores
        }
    }

    /**
     * Get segment name for location (placeholder - would need actual mapping)
     */
    private function getSegmentForLocation(OrganisationLocations $location): string
    {
        // This would typically come from a related table or be stored in the location
        // For now, return a default based on location name or organization

        // Simple logic based on location name
        $locationName = strtolower($location->location_name);
        if (strpos($locationName, 'warehouse') !== false || strpos($locationName, 'rdc') !== false) {
            return 'Warehousing';
        } elseif (strpos($locationName, 'express') !== false || strpos($locationName, 'superstore') !== false) {
            return 'Retail';
        } else {
            return 'Property Owners'; // Default fallback
        }
    }

    /**
     * Get Treaty Class Occupancy for location (placeholder - would need actual mapping)
     */
    private function getTcoForLocation(OrganisationLocations $location): string
    {
        // This would typically come from a related table or be stored in the location
        // For now, return a default based on location name or type

        // Simple logic based on location name
        $locationName = strtolower($location->location_name);
        if (strpos($locationName, 'warehouse') !== false || strpos($locationName, 'rdc') !== false) {
            return 'Cold Stores';
        } elseif (strpos($locationName, 'centre') !== false || strpos($locationName, 'shopping') !== false) {
            return 'Shopping Centres';
        } else {
            return 'Preserved and deep frozen food'; // Default fallback
        }
    }
}
