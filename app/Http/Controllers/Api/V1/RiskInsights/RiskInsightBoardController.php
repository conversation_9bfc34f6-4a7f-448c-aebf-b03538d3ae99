<?php

namespace App\Http\Controllers\Api\V1\RiskInsights;

use App\Http\Controllers\Controller;
use App\Models\RiskInsights\DocumentRequest;
use App\Models\RiskInsights\RiskInsightAuditLogs;
use Illuminate\Http\Request;

class RiskInsightBoardController extends Controller
{
    public function getDocumentsForBoard()
    {
        // Get document requests based on status
        $openDocuments = DocumentRequest::open()
            ->select(['id', 'assigned_to_user', 'status'])
            ->get();
            
        $underReviewDocuments = DocumentRequest::with('assignedUser')
            ->underReview()
            ->select(['id', 'assigned_to_user', 'status'])
            ->get();
            
        $completedDocuments = DocumentRequest::with('assignedUser')
            ->completed()
            ->select(['id', 'assigned_to_user', 'status'])
            ->get();

        // Get document IDs for each status
        $openDocumentIds = $openDocuments->pluck('id');
        $underReviewDocumentIds = $underReviewDocuments->pluck('id');
        $completedDocumentIds = $completedDocuments->pluck('id');

        // Get latest audit logs for all documents
        $auditLogs = RiskInsightAuditLogs::whereIn('document_id', 
            $openDocumentIds->concat($underReviewDocumentIds)->concat($completedDocumentIds)
        )
        ->select(['id', 'document_id', 'metadata_update', 'version', 'created_at'])
        ->orderBy('version', 'desc')
        ->get()
        ->groupBy('document_id')
        ->map(function ($logs) {
            return $logs->first(); // Get the latest version for each document
        });

        $response = [
            'open' => [
                'documents' => $openDocuments->map(function ($document) use ($auditLogs) {
                    $auditLog = $auditLogs->get($document->id);
                    if (!$auditLog) return null;
                    return [
                        'id' => $document->id,
                        'audit_log' => [
                            'id' => $auditLog->id,
                            'metadata_update' => $auditLog->metadata_update,
                            'created_at' => $auditLog->created_at->format('Y-m-d H:i:s')
                        ]
                    ];
                })->filter()->values()
            ],
            'underReview' => [
                'documents' => $underReviewDocuments->map(function ($document) use ($auditLogs) {
                    $auditLog = $auditLogs->get($document->id);
                    if (!$auditLog) return null;
                    
                    $assignedToUserName = !empty($document->assignedUser) ? $document->assignedUser->first_name . ' ' . $document->assignedUser->last_name : '';
                    
                    return [
                        'id' => $document->id,
                        'assigned_to_user_id' => $document->assigned_to_user,
                        'assigned_to_user' => $assignedToUserName,
                        'audit_log' => [
                            'id' => $auditLog->id,
                            'metadata_update' => $auditLog->metadata_update,
                            'created_at' => $auditLog->created_at->format('Y-m-d H:i:s')
                        ]
                    ];
                })->filter()->values()
            ],
            'completed' => [
                'documents' => $completedDocuments->map(function ($document) use ($auditLogs) {
                    $auditLog = $auditLogs->get($document->id);
                    if (!$auditLog) return null;
                    
                    $assignedToUserName = !empty($document->assignedUser) ? $document->assignedUser->first_name . ' ' . $document->assignedUser->last_name : '';

                    return [
                        'id' => $document->id,
                        'assigned_to_user_id' => $document->assigned_to_user,
                        'assigned_to_user' => $assignedToUserName,
                        'audit_log' => [
                            'id' => $auditLog->id,
                            'metadata_update' => $auditLog->metadata_update,
                            'created_at' => $auditLog->created_at->format('Y-m-d H:i:s')
                        ]
                    ];
                })->filter()->values()
            ]
        ];

        return response()->json($response);
    }

    public function documentAssignment(Request $request)
    {
        $documentId = $request->document_id;
        $newRiskEngineerId = $request->assigned_to === 'unassign' ? null : $request->assigned_to;

        $document = DocumentRequest::find($documentId);
        
        if (!$document) {
            return response()->json([
                'success' => false,
                'message' => 'Document not found'
            ], 404);
        }

        $document->assigned_to_user = $newRiskEngineerId;
        $document->save();

        return response()->json([
            'success' => true,
            'message' => 'Document re-assigned successfully'
        ]);
    }
}
