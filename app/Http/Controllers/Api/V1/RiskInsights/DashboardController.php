<?php

namespace App\Http\Controllers\Api\V1\RiskInsights;

use App\Http\Controllers\Controller;
use App\Models\Organisation;
use App\Models\RiskGrading\RgSubAttributeScore;
use App\Models\RRAppetite\Subsector;
use App\Models\Sector;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\File;

class DashboardController extends Controller
{
    public function index()
    {
        $filePath = public_path('templates-league/data/default/dashboard.json');
        return response()->json(json_decode(file_get_contents($filePath), true));
    }

    public function location(Request $request)
    {
        // $dashboardLocation = file_get_contents(public_path('templates-league/data/default/dashboard_location.json'));
        // return response()->json(json_decode($dashboardLocation, true));
        $scores = RgSubAttributeScore::with([
            'organisationLocation:id,organisation_id,location_name,postcode,tiv',
            'organisationLocation.organisation:id,name,logo,premium',
            'subAttribute.attribute'
        ])
        ->whereHas('organisationLocation', function($query) use ($request) {
            $query->where('organisation_id', $request->organisation_id);
        })
        ->get();

        // Extract unique attribute names first
        $attributeNames = $scores->map(function($score) {
            return $score->subAttribute->attribute->attribute;
        })->unique()->values()->toArray();

        $locationData = $scores->groupBy('organisation_location_id')
        ->map(function($locationScores) {
            $location = $locationScores->first()->organisationLocation;
            $organisation = $location->organisation;
            
            // Initialize the response structure
            $response = [
                'id' => $location->id,
                'organisation_id' => $location->organisation_id,
                'organisation_name' => $organisation->name,
                'organisation_logo' => $organisation->logo,
                'location_name' => $location->location_name,
                'postcode' => $location->postcode,
                'tiv' => $location->tiv,
                'premium' => $organisation->premium ?? null,
                'risk_scores' => []
            ];

            // Group scores by attribute
            $scoresByAttribute = [];
            foreach ($locationScores as $score) {
                $attributeName = $score->subAttribute->attribute->attribute;
                if (!isset($scoresByAttribute[$attributeName])) {
                    $scoresByAttribute[$attributeName] = [
                        'attribute' => $attributeName,
                        'score' => 0, // Will be calculated as average
                        'sub_scores' => []
                    ];
                }

                $riskScore = $score->max_score > 0 ? ($score->score / $score->max_score) * 100 : $score->score;

                $scoresByAttribute[$attributeName]['sub_scores'][] = [
                    'score' => round($riskScore),
                    'risk_grading' => $score->risk_grading
                ];
            }

            // Calculate average scores for each attribute
            foreach ($scoresByAttribute as &$attributeData) {
                $totalScore = 0;
                $validScores = 0;
                
                foreach ($attributeData['sub_scores'] as $subScore) {
                    if ($subScore['score'] > 0) {
                        $totalScore += $subScore['score'];
                        $validScores++;
                    }
                }

                $attributeData['score'] = $validScores > 0 ? round($totalScore / $validScores) : 0;
            }

            $response['risk_scores'] = array_values($scoresByAttribute);
            return $response;
        })
        ->values();

        return response()->json([
            'status' => 'success',
            'attributes' => $attributeNames,
            'data' => $locationData
        ]);
    }

    public function company(Request $request)
    {

        if (!$request->has('organisation_id')) {
            return response()->json([
                'status' => 'error',
                'message' => 'Organisation ID is required'
            ], 400);
        }
        
        $details = Organisation::where('id', $request->organisation_id)
            ->select([
                'id',
                'name',
                'logo',
                'premium',
                'position',
                'line_size',
                'expiry_date_of_cover',
                'underwriter',
                'risk_score',
                'policy_segment',
                'broker_id',
                'sector',
                'sub_sector_id',
                'lead_insurer_id',
                'policy_segment',
            ])
            ->with([
                'broker:id,name,image',
                'leadInsurer:id,name,image',
                'sector:id,handle,type',
                'subSector:id,handle,type',
            ])
            ->first();

        return response()->json([
            'status' => 'success',
            'data' => $details
        ]);
    }

    public function risk()
    {
        $riskDashboard = file_get_contents(public_path('templates-league/data/default/risk_dashboard.json'));
        return response()->json(json_decode($riskDashboard, true));
    }

    public function getFilters()
    {
        $filters = [];

        $filters = Sector::with('subSector:id,sector_id,handle,type')
        ->select([
            'id',
            'handle',
            'type',
        ])->get();

        return response()->json([
            'status' => 'success',
            'data' => $filters,
        ]);
    }
}
