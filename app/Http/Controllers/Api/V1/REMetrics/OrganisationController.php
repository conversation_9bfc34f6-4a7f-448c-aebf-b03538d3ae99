<?php

namespace App\Http\Controllers\Api\V1\REMetrics;

use App\Http\Controllers\Api\V1\ScheduleController;
use App\Http\Controllers\BaseController;
use App\Models\Organisation;
use App\Models\OrganisationOverviewLog;
use App\Models\OrganisationPolicy;
use App\Models\ReMetricsLog;
use App\Models\RiskImprovementFormySubmissions;
use App\Models\RiskRecommendationCards;
use App\Models\ScheduleMeta;
use App\Models\Survey;
use App\Models\SurveyCards;
use Carbon\Carbon;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class OrganisationController extends BaseController
{
    const RISK_MAPPING = [
        'A' => 5,
        'B' => 4,
        'C' => 3,
        'D' => 2,
        'E' => 1
    ];

    const ACTIVE_ORGANISATIONS_LIMIT = 5;

    public function getAccountsUnderManagement(Request $request): JsonResponse
    {
        [$startDate, $endDate] = $this->getDateRangeInput($request);
        $startDate = $startDate ?? now()->subYear()->endOfDay();
        $endDate = $endDate ?? now()->subMonth(1)->lastOfMonth()->endOfDay();

        $accounts = ReMetricsLog::accountsUnderManagement()
            ->when($startDate, function ($query, $startDate) {
                return $query->where('created_at', '>=', $startDate);
            })
            ->when($endDate, function ($query, $endDate) {
                return $query->where('created_at', '<=', $endDate);
            })
            ->orderBy('created_at')
            ->get();

        $transformedData = $accounts->map(function ($item) {
            return [
                'month' => $item->created_at->format('M'),
                'value' => $item->processed_data,
            ];
        })->toArray();

        return response()->json([
            'chart_data' => $transformedData,
            'data' => $accounts,
        ]);
    }

    public function getAverageRiskGrading(Request $request): JsonResponse
    {
        [$startDate, $endDate] = $this->getDateRangeInput($request);
        $startDate = $startDate ?? now()->subYear()->endOfDay();
        $endDate = $endDate ?? now()->subMonth(1)->lastOfMonth()->endOfDay();

        $averageRiskGrading = ReMetricsLog::averageRiskGrading()
            ->when($startDate, function ($query, $startDate) {
                return $query->where('created_at', '>=', $startDate);
            })
            ->when($endDate, function ($query, $endDate) {
                return $query->where('created_at', '<=', $endDate);
            })
            ->orderBy('created_at')
            ->get();

        $transformedData = $averageRiskGrading->map(function ($item) {
            return [
                'month' => $item->created_at->format('M'),
                'value' => $item->processed_data['average_value'],
                'grade' => $item->processed_data['grade'],
            ];
        })->toArray();

        return response()->json([
            'chart_data' => $transformedData,
            'data' => $averageRiskGrading,
        ]);
    }

    public function getActiveOrganizations(Request $request, $isInactive = false): JsonResponse
    {
        $sectorId = $request->input('sectorId', '');
        $brokerId = $request->input('brokerId', '');
        $policyTypeId = $request->input('policyTypeId', '');
        $underwriterId = $request->input('underwriterId', '');
        $accountEngineerId = $request->input('accountEngineerId', '');
        [$startDate, $endDate] = $this->getDateRangeInput($request);
        $endDate = $endDate ?? now();

        if($policyTypeId){
            $organisationIds = OrganisationPolicy::where('policy_type_id', $policyTypeId)
            ->whereNull('deleted_at')
            ->pluck('organisation_id');
        }

        $organisations = Organisation::select('id')
            ->bound()
            ->when($sectorId, function ($query, $sectorId) {
                return $query->where('sector', $sectorId);
            })
            ->when($brokerId, function ($query, $brokerId) {
                return $query->where('broker_id', $brokerId);
            })
            ->when($policyTypeId, fn($query, $policyTypeId) =>
                $query->whereExists(function ($subquery) use ($policyTypeId) {
                    $subquery->select(DB::raw(1))
                        ->from('organisation_policy_numbers')
                        ->whereColumn('organisation_policy_numbers.organisation_id', 'organisations.id')
                        ->where('organisation_policy_numbers.policy_type_id', $policyTypeId)
                        ->whereNull('organisation_policy_numbers.deleted_at');
                })
            )
            ->when($underwriterId, function ($query, $underwriterId) {
                return $query->whereHas('contacts', function ($subquery) use ($underwriterId) {
                    $subquery->where('user_id', $underwriterId);
                });
            })
            ->when($accountEngineerId, function ($query, $accountEngineerId) {
                return $query->whereHas('contacts', function ($subquery) use ($accountEngineerId) {
                    $subquery->where('user_id', $accountEngineerId);
                });
            })
            ->when($startDate, function ($query, $startDate) {
                return $query->where('created_at', '>=', $startDate);
            })
            ->when($endDate, function ($query, $endDate) {
                return $query->where('created_at', '<=', $endDate);
            })
            ->get();
            

        // Get the survey with latest survey date for each organisation
        $surveyData = DB::table(function ($query) use ($organisations, $endDate) {
            $query->select(
                'organisations.id as organisation_id',
                'organisations.name as organisation_name',
                'organisations.logo as logo',
                'schedule_meta.value as date',
                'surveys.id as survey_id',
                'schedule.id as schedule_id',
                DB::raw('IFNULL(NULL, "Survey") as last_touchpoint'),
                DB::raw('ROW_NUMBER() OVER (PARTITION BY organisations.id ORDER BY schedule_meta.value DESC) as rn')
            )
                ->from('schedule_meta')
                ->join('schedule', 'schedule_meta.schedule_id', '=', 'schedule.id')
                ->join('surveys', 'schedule.id', '=', 'surveys.schedule_id')
                ->join('organisations', 'surveys.organisation_id', '=', 'organisations.id')
                ->where('schedule_meta.key', '=', 'actual_submission_deadline')
                ->where('schedule_meta.value', '<=', $endDate)
                ->whereIn('organisations.id', $organisations->pluck('id')->toArray());
        }, 'ranked')
            ->where('rn', 1)
            ->orderBy('organisation_id')
            ->get();
        // return response()->json(surveyData);

        // Get all latest calendar schedules for each organisation
        $scheduleData = DB::table(function ($query) use ($organisations, $endDate) {
            $query->select(
                'schedule_meta.value as organisation_id',
                'organisations.name as organisation_name',
                'organisations.logo as logo',
                'schedule.start as date',
                'schedule.id as schedule_id',
                DB::raw('ROW_NUMBER() OVER (PARTITION BY schedule_meta.value ORDER BY schedule.start DESC) as rn')
            )
                ->from('schedule')
                ->join('schedule_meta', 'schedule.id', '=', 'schedule_meta.schedule_id')
                ->join('organisations', 'schedule_meta.value', '=', 'organisations.id')
                ->whereNotIn('schedule.type', ['survey'])
                ->where('schedule_meta.key', '=', 'client_organisation_id')
                ->where('schedule.start', '<=', $endDate)
                ->whereIn('schedule_meta.value', $organisations->pluck('id')->toArray());
        }, 'ranked')
            ->where('rn', 1)
            ->orderBy('organisation_id')
            ->get();

        $scheduleEventTypes = ScheduleMeta::where('key', '=', 'event_types')
            ->whereIn('schedule_id', $scheduleData->pluck('schedule_id')->toArray())
            ->get();

        $scheduleData->each(function ($item) use ($scheduleEventTypes) {
            $eventTypeId = $scheduleEventTypes->where('schedule_id', $item->schedule_id)->first()?->value;
            if ($eventTypeId) {
                $item->last_touchpoint = ScheduleController::$event_types[$eventTypeId];
            }
        });

        // Merge both collections fetching the latest touchpoint for each organisation
        $mergedData = collect([...$surveyData, ...$scheduleData]);

        // Ensure each item has a last_touchpoint property
        $mergedData = $mergedData->map(function ($item) {
            if (!property_exists($item, 'last_touchpoint')) {
                $item->last_touchpoint = null;
            }
            return $item;
        });

        // Filter out items without a last_touchpoint
        $mergedData = $mergedData->filter(function ($item) {
            return !is_null($item->last_touchpoint);
        });
        
        $mergedData = $mergedData->sortByDesc('date');
        $mergedData = $mergedData->unique('organisation_id')->values();

        $limit = $request->input('limit', self::ACTIVE_ORGANISATIONS_LIMIT);

        // For inactive organisations, we want to get the organisation with the oldest touchpoint
        if ($isInactive) {
            $last = $mergedData->sortBy('date')->values()->take($limit);
            return response()->json($last);
        }

        return response()->json($mergedData->take($limit));
    }

    public function getInactiveOrganizations(Request $request): JsonResponse
    {
        return $this->getActiveOrganizations($request, true);
    }

    public function getOrganisationCountBySector(Request $request): JsonResponse
    {
        $sectorId = $request->input('sectorId', '');
        $brokerId = $request->input('brokerId', '');
        $policyTypeId = $request->input('policyTypeId', '');
        $underwriterId = $request->input('underwriterId', '');
        $accountEngineerId = $request->input('accountEngineerId', '');

        [$startDate, $endDate] = $this->getDateRangeInput($request);

        $orgModel = new Organisation();
        $orgModel::$withoutAppends = true;

        $sectorByOrganisationCount = $orgModel
            ->select(DB::raw('count(organisations.id) as value, sectors.handle as category'))
            ->bound()
            ->join('sectors', 'organisations.sector', '=', 'sectors.id')
            ->when($startDate, function ($query, $startDate) {
                return $query->where('organisations.created_at', '>=', $startDate);
            })
            ->when($endDate, function ($query, $endDate) {
                return $query->where('organisations.created_at', '<=', $endDate);
            })
            ->when($sectorId, function ($query, $sectorId) {
                return $query->where('organisations.sector', $sectorId);
            })
            ->when($brokerId, function ($query, $brokerId) {
                return $query->where('organisations.broker_id', $brokerId);
            })
            ->when($policyTypeId, fn($query, $policyTypeId) =>
                $query->whereExists(function ($subquery) use ($policyTypeId) {
                    $subquery->select(DB::raw(1))
                        ->from('organisation_policy_numbers')
                        ->whereColumn('organisation_policy_numbers.organisation_id', 'organisations.id')
                        ->where('organisation_policy_numbers.policy_type_id', $policyTypeId)
                        ->whereNull('organisation_policy_numbers.deleted_at');
                })
            )
            ->when($underwriterId, function ($query, $underwriterId) {
                return $query->whereHas('contacts', function ($subquery) use ($underwriterId) {
                    $subquery->where('user_id', $underwriterId);
                });
            })
            ->when($accountEngineerId, function ($query, $accountEngineerId) {
                return $query->whereHas('contacts', function ($subquery) use ($accountEngineerId) {
                    $subquery->where('user_id', $accountEngineerId);
                });
            })
            ->groupBy('sectors.id')
            ->orderBy('value', 'desc')
            ->get();

        return response()->json($sectorByOrganisationCount);
    }

    public function getRiskEngineeringMeetingHeld(Request $request): JsonResponse
    {
        $sectorId = $request->input('sectorId', '');
        $brokerId = $request->input('brokerId', '');
        $policyTypeId = $request->input('policyTypeId', '');
        $underwriterId = $request->input('underwriterId', '');
        $accountEngineerId = $request->input('accountEngineerId', '');

        [$startDate, $endDate] = $this->getDateRangeInput($request);

        $reMeetings = ScheduleMeta::where('key', '=', 'event_types')
            ->where('value', '=', '1')
            ->get();

        $organisations = Organisation::withoutAppends()
            ->select('id')
            ->bound()
            ->when($sectorId, function ($query, $sectorId) {
                return $query->where('sector', $sectorId);
            })
            ->when($brokerId, function ($query, $brokerId) {
                return $query->where('broker_id', $brokerId);
            })
            ->when($policyTypeId, fn($query, $policyTypeId) =>
                $query->whereExists(function ($subquery) use ($policyTypeId) {
                    $subquery->select(DB::raw(1))
                        ->from('organisation_policy_numbers')
                        ->whereColumn('organisation_policy_numbers.organisation_id', 'organisations.id')
                        ->where('organisation_policy_numbers.policy_type_id', $policyTypeId)
                        ->whereNull('organisation_policy_numbers.deleted_at');
                })
            )
            ->when($underwriterId, function ($query, $underwriterId) {
                return $query->whereHas('contacts', function ($subquery) use ($underwriterId) {
                    $subquery->where('user_id', $underwriterId);
                });
            })
            ->when($accountEngineerId, function ($query, $accountEngineerId) {
                return $query->whereHas('contacts', function ($subquery) use ($accountEngineerId) {
                    $subquery->where('user_id', $accountEngineerId);
                });
            })
            ->whereHas('contacts', function ($query) {
                $query->where('type', 'risk-engineer');
            })
            ->get();

        $scheduleMeta = ScheduleMeta::query()
            ->select('value') // Only select the organisation ID
            ->where('key', 'client_organisation_id')
            ->whereIn('schedule_id', $reMeetings->pluck('schedule_id'))
            ->whereIn('value', $organisations->pluck('id'))
            ->when($startDate, function ($query, $startDate) {
                return $query->where('schedule_meta.created_at', '>=', $startDate);
            })
            ->when($endDate, function ($query, $endDate) {
                return $query->where('schedule_meta.created_at', '<=', $endDate);
            })
            ->distinct()
            ->get();
        

        return response()->json([
            [
                'category' => 'Yes',
                'value' => $scheduleMeta->count(),
            ],
            [
                'category' => 'No',
                'value' => ($organisations->count() - $scheduleMeta->count()),
            ],
        ]);
    }

    public function getOrganisationOverviewUpdated(Request $request): JsonResponse
    {
        $sectorId = $request->input('sectorId', '');
        $brokerId = $request->input('brokerId', '');
        $policyTypeId = $request->input('policyTypeId', '');
        $underwriterId = $request->input('underwriterId', '');
        $accountEngineerId = $request->input('accountEngineerId', '');
        [$startDate, $endDate] = $this->getDateRangeInput($request);

        $organisations = Organisation::withoutAppends()
            ->select('id')
            ->bound()
            ->when($sectorId, function ($query, $sectorId) {
                return $query->where('sector', $sectorId);
            })
            ->when($brokerId, function ($query, $brokerId) {
                return $query->where('broker_id', $brokerId);
            })
            ->when($policyTypeId, fn($query, $policyTypeId) =>
                $query->whereExists(function ($subquery) use ($policyTypeId) {
                    $subquery->select(DB::raw(1))
                        ->from('organisation_policy_numbers')
                        ->whereColumn('organisation_policy_numbers.organisation_id', 'organisations.id')
                        ->where('organisation_policy_numbers.policy_type_id', $policyTypeId)
                        ->whereNull('organisation_policy_numbers.deleted_at');
                })
            )
            ->when($underwriterId, function ($query, $underwriterId) {
                return $query->whereHas('contacts', function ($subquery) use ($underwriterId) {
                    $subquery->where('user_id', $underwriterId);
                });
            })
            ->when($accountEngineerId, function ($query, $accountEngineerId) {
                return $query->whereHas('contacts', function ($subquery) use ($accountEngineerId) {
                    $subquery->where('user_id', $accountEngineerId);
                });
            })
            ->whereHas('contacts', function ($query) {
                $query->where('type', 'risk-engineer');
            })
            ->get();

        $organisationOverviewUpdated = OrganisationOverviewLog::whereIn('organisation_id', $organisations->pluck('id')->map(function ($id) {
                return (string) $id;
            }))
            ->when($startDate, function ($query, $startDate) {
                return $query->where('created_at', '>=', $startDate);
            })
            ->when($endDate, function ($query, $endDate) {
                return $query->where('created_at', '<=', $endDate);
            })
            ->distinct('organisation_id')
            ->get();

        return response()->json([
            [
                'category' => 'Yes',
                'value' => $organisationOverviewUpdated->count(),
            ],
            [
                'category' => 'No',
                'value' => ($organisations->count() - $organisationOverviewUpdated->count()),
            ],
        ]);
    }

    public function getOrganisationWithSurveyProgramme(Request $request): JsonResponse
    {
        [$startDate, $endDate] = $this->getDateRangeInput($request);
        $sectorId = $request->input('sectorId', '');
        $brokerId = $request->input('brokerId', '');
        $policyTypeId = $request->input('policyTypeId', '');
        $underwriterId = $request->input('underwriterId', '');
        $accountEngineerId = $request->input('accountEngineerId', '');

        $organisations = Organisation::withoutAppends()
            ->select('id')
            ->bound()
            ->when($sectorId, function ($query, $sectorId) {
                return $query->where('sector', $sectorId);
            })
            ->when($brokerId, function ($query, $brokerId) {
                return $query->where('broker_id', $brokerId);
            })
            ->when($policyTypeId, fn($query, $policyTypeId) =>
                $query->whereExists(function ($subquery) use ($policyTypeId) {
                    $subquery->select(DB::raw(1))
                        ->from('organisation_policy_numbers')
                        ->whereColumn('organisation_policy_numbers.organisation_id', 'organisations.id')
                        ->where('organisation_policy_numbers.policy_type_id', $policyTypeId)
                        ->whereNull('organisation_policy_numbers.deleted_at');
                })
            )
            ->when($underwriterId, function ($query, $underwriterId) {
                return $query->whereHas('contacts', function ($subquery) use ($underwriterId) {
                    $subquery->where('user_id', $underwriterId);
                });
            })
            ->when($accountEngineerId, function ($query, $accountEngineerId) {
                return $query->whereHas('contacts', function ($subquery) use ($accountEngineerId) {
                    $subquery->where('user_id', $accountEngineerId);
                });
            })
            ->whereHas('contacts', function ($query) {
                $query->where('type', 'risk-engineer');
            })
            ->get();

        $organisationWithSurveyProgramme = Organisation::withoutAppends()
            ->select('id')
            ->whereHas('surveys', function ($query) use ($startDate, $endDate) {
                if ($startDate) {
                    $query->where('created_at', '>=', $startDate);
                }
                if ($endDate) {
                    $query->where('created_at', '<=', $endDate);
                }
            })
            ->bound()
            ->when($sectorId, function ($query, $sectorId) {
                return $query->where('sector', $sectorId);
            })
            ->when($brokerId, function ($query, $brokerId) {
                return $query->where('broker_id', $brokerId);
            })
            ->when($policyTypeId, fn($query, $policyTypeId) =>
                $query->whereExists(function ($subquery) use ($policyTypeId) {
                    $subquery->select(DB::raw(1))
                        ->from('organisation_policy_numbers')
                        ->whereColumn('organisation_policy_numbers.organisation_id', 'organisations.id')
                        ->where('organisation_policy_numbers.policy_type_id', $policyTypeId)
                        ->whereNull('organisation_policy_numbers.deleted_at');
                })
            )
            ->when($underwriterId, function ($query, $underwriterId) {
                return $query->whereHas('contacts', function ($subquery) use ($underwriterId) {
                    $subquery->where('user_id', $underwriterId);
                });
            })
            ->when($accountEngineerId, function ($query, $accountEngineerId) {
                return $query->whereHas('contacts', function ($subquery) use ($accountEngineerId) {
                    $subquery->where('user_id', $accountEngineerId);
                });
            })
            ->whereHas('contacts', function ($query) {
                $query->where('type', 'risk-engineer');
            })
            ->get();

        return response()->json([
            [
                'category' => 'Yes',
                'value' => $organisationWithSurveyProgramme->count(),
            ],
            [
                'category' => 'No',
                'value' => ($organisations->count() - $organisationWithSurveyProgramme->count()),
            ],
        ]);
    }

    public function getRiskRecommendationCountByStatus(Request $request): JsonResponse
    {
        $sectorId = $request->input('sectorId', '');
        $brokerId = $request->input('brokerId', '');
        $policyTypeId = $request->input('policyTypeId', '');
        $underwriterId = $request->input('underwriterId', '');
        $accountEngineerId = $request->input('accountEngineerId', '');
        [$startDate, $endDate] = $this->getDateRangeInput($request);

        $organisations = Organisation::withoutAppends()
            ->select('id')
            ->bound()
            ->when($sectorId, function ($query, $sectorId) {
                return $query->where('sector', $sectorId);
            })
            ->when($brokerId, function ($query, $brokerId) {
                return $query->where('broker_id', $brokerId);
            })
            ->when($policyTypeId, fn($query, $policyTypeId) =>
                $query->whereExists(function ($subquery) use ($policyTypeId) {
                    $subquery->select(DB::raw(1))
                        ->from('organisation_policy_numbers')
                        ->whereColumn('organisation_policy_numbers.organisation_id', 'organisations.id')
                        ->where('organisation_policy_numbers.policy_type_id', $policyTypeId)
                        ->whereNull('organisation_policy_numbers.deleted_at');
                })
            )
            ->when($underwriterId, function ($query, $underwriterId) {
                return $query->whereHas('contacts', function ($subquery) use ($underwriterId) {
                    $subquery->where('user_id', $underwriterId);
                });
            })
            ->when($accountEngineerId, function ($query, $accountEngineerId) {
                return $query->whereHas('contacts', function ($subquery) use ($accountEngineerId) {
                    $subquery->where('user_id', $accountEngineerId);
                });
            })
            ->get();

        $riskRecommendationCards = RiskRecommendationCards::raw(function ($collection) use ($organisations, $startDate, $endDate) {
            $matchConditions = [
                'organisation_id' => ['$in' => $organisations->pluck('id')->toArray()]
            ];

            // Add the startDate condition if it is provided
            if ($startDate) {
                $matchConditions['created_at'] = ['$gte' => new \MongoDB\BSON\UTCDateTime(strtotime($startDate) * 1000)];
            }

            if ($endDate) {
                $matchConditions['created_at'] = ['$lte' => new \MongoDB\BSON\UTCDateTime(strtotime($endDate) * 1000)];
            }

            return $collection->aggregate([
                [
                    '$match' => $matchConditions
                ],
                [
                    '$group' => [
                        '_id' => '$column',
                        'count' => ['$sum' => 1]
                    ]
                ],
                [
                    '$project' => [
                        'column' => '$_id',
                        'count' => 1,
                        '_id' => 0
                    ]
                ]
            ]);
        });

        $status = [
            'backlog' => 'Open',
            'feedback-received' => 'Feedback Received',
            'past-due' => 'Overdue',
            'closed' => 'Closed',
        ];

        $transformedData = $riskRecommendationCards->map(function ($item) use ($status) {
            return [
                'category' => $status[$item->column] ?? $item->column,
                'value' => $item->count,
            ];
        })->toArray();

        return response()->json($transformedData);
    }

    public function getSrfs(Request $request)
    {
        $sectorId = $request->input('sectorId', '');
        $brokerId = $request->input('brokerId', '');
        $policyTypeId = $request->input('policyTypeId', '');
        $underwriterId = $request->input('underwriterId', '');
        $accountEngineerId = $request->input('accountEngineerId', '');
        [$startDate, $endDate] = $this->getDateRangeInput($request);

        $status = [
            'outsourced' => ['failed' => 0, 'passed' => 0, 'inProgress' => 0, 'notStarted' => 0],
            'internal' => ['failed' => 0, 'passed' => 0, 'inProgress' => 0, 'notStarted' => 0],
        ];

        // Process surveys in batches of 500
        Survey::with('scheduleMeta', 'organisation.contacts')
            ->where('survey_type', '=', 'survey')
            ->when($startDate, fn($q) => $q->where('created_at', '>=', $startDate))
            ->when($endDate, fn($q) => $q->where('created_at', '<=', $endDate))
            ->whereHas('organisation', function ($query) use (
                $sectorId, $brokerId, $policyTypeId, $underwriterId, $accountEngineerId
            ) {
                $query->bound()
                    ->when($sectorId, fn($q) => $q->where('sector', $sectorId))
                    ->when($brokerId, fn($q) => $q->where('broker_id', $brokerId))
                    ->when($policyTypeId, function ($q) use ($policyTypeId) {
                        $q->whereExists(function ($subquery) use ($policyTypeId) {
                            $subquery->select(DB::raw(1))
                                ->from('organisation_policy_numbers')
                                ->whereColumn('organisation_policy_numbers.organisation_id', 'organisations.id')
                                ->where('organisation_policy_numbers.policy_type_id', $policyTypeId)
                                ->whereNull('organisation_policy_numbers.deleted_at');
                        });
                    })
                    ->when($underwriterId, fn($q) =>
                        $q->whereHas('contacts', fn($sub) => $sub->where('user_id', $underwriterId))
                    )
                    ->when($accountEngineerId, fn($q) =>
                        $q->whereHas('contacts', fn($sub) => $sub->where('user_id', $accountEngineerId))
                    );
            })
            ->chunk(500, function ($srfs) use (&$status) {
                $surveyIds = $srfs->pluck('id')->map(fn($id) => (string) $id)->toArray();

                // Batch MongoDB aggregation for these survey IDs
                $submissions = collect(RiskImprovementFormySubmissions::raw(function ($collection) use ($surveyIds) {
                    return $collection->aggregate([
                        ['$match' => ['survey_id' => ['$in' => $surveyIds]]],
                        ['$project' => [
                            'survey_id' => 1,
                            'csr_submission_date' => 1,
                            'uwr_status' => 1,
                            'csr_status' => 1,
                            '_id' => 0
                        ]]
                    ]);
                }));

                $submissionMap = $submissions->keyBy('survey_id');

                $srfs->each(function ($survey) use ($submissionMap, &$status) {
                    $type = $survey->external_survey_company_id ? 'outsourced' : 'internal';
                    $submission = $submissionMap[(string) $survey->id] ?? null;
                    $scheduleMeta = $survey->scheduleMeta->value ?? null;

                    if (!empty($submission['csr_submission_date']) && !empty($scheduleMeta)) {
                        try {
                            $submissionDate = Carbon::createFromFormat('d/m/Y', $submission['csr_submission_date']);
                            $deadline = Carbon::parse($scheduleMeta)->addDays(14);
                            $status[$type][$submissionDate->isAfter($deadline) ? 'failed' : 'passed']++;
                        } catch (\Exception $e) {
                            $status[$type]['notStarted']++;
                        }
                    } elseif (
                        !empty($submission['csr_status']) && !empty($submission['uwr_status']) &&
                        $submission['csr_status'] !== 'submitted' &&
                        $submission['uwr_status'] !== 'submitted'
                    ) {
                        $status[$type]['inProgress']++;
                    } else {
                        $status[$type]['notStarted']++;
                    }
                });
            });

        // Format response
        $responseData = [];
        foreach ($status as $type => $statuses) {
            $responseData[$type] = [
                ['category' => 'Completed - SLA Passed', 'value' => $statuses['passed']],
                ['category' => 'Completed - SLA Failed', 'value' => $statuses['failed']],
                ['category' => 'In Progress', 'value' => $statuses['inProgress']],
                ['category' => 'Not Started', 'value' => $statuses['notStarted']],
            ];
        }

        return response()->json($responseData);
    }

    public function getDtrs(Request $request)
    {
        $sectorId = $request->input('sectorId', '');
        $brokerId = $request->input('brokerId', '');
        $policyTypeId = $request->input('policyTypeId', '');
        $underwriterId = $request->input('underwriterId', '');
        $accountEngineerId = $request->input('accountEngineerId', '');
        [$startDate, $endDate] = $this->getDateRangeInput($request);
        $organisations = Organisation::withoutAppends()
            ->select('id')
            ->bound()
            ->when($sectorId, function ($query, $sectorId) {
                return $query->where('sector', $sectorId);
            })
            ->when($brokerId, function ($query, $brokerId) {
                return $query->where('broker_id', $brokerId);
            })
            ->when($policyTypeId, fn($query, $policyTypeId) =>
                $query->whereExists(function ($subquery) use ($policyTypeId) {
                    $subquery->select(DB::raw(1))
                        ->from('organisation_policy_numbers')
                        ->whereColumn('organisation_policy_numbers.organisation_id', 'organisations.id')
                        ->where('organisation_policy_numbers.policy_type_id', $policyTypeId)
                        ->whereNull('organisation_policy_numbers.deleted_at');
                })
            )
            ->when($underwriterId, function ($query, $underwriterId) {
                return $query->whereHas('contacts', function ($subquery) use ($underwriterId) {
                    $subquery->where('user_id', $underwriterId);
                });
            })
            ->when($accountEngineerId, function ($query, $accountEngineerId) {
                return $query->whereHas('contacts', function ($subquery) use ($accountEngineerId) {
                    $subquery->where('user_id', $accountEngineerId);
                });
            })
            ->get();

        $srfs = Survey::with('schedule')
            ->whereIn('organisation_id', $organisations->pluck('id')->toArray())
            ->where('survey_type', '=', 'dtr')
            ->when($startDate, function ($query, $startDate) {
                return $query->where('created_at', '>=', $startDate);
            })
            ->when($endDate, function ($query, $endDate) {
                return $query->where('created_at', '<=', $endDate);
            })
            ->get();

        $submissions = RiskImprovementFormySubmissions::raw(function ($collection) use ($srfs) {
            $surveyIds = $srfs->pluck('id')
                ->map(function ($id) {
                    return (string) $id;
                })
                ->toArray();

            return $collection->aggregate([
                [
                    '$match' => [
                        'survey_id' => ['$in' => $surveyIds]
                    ]
                ],
                [
                    '$project' => [
                        'survey_id' => 1,
                        'csr_submission_date' => 1,
                        'uwr_status' => 1,
                        'csr_status' => 1,
                        '_id' => 0
                    ]
                ]
            ]);
        });

        $status = [
            'outsourced' => [
                'failed' => 0,
                'passed' => 0,
                'inProgress' => 0,
                'notStarted' => 0,
            ],
            'internal' => [
                'failed' => 0,
                'passed' => 0,
                'inProgress' => 0,
                'notStarted' => 0,
            ]
        ];

        $srfs->each(function ($survey) use ($submissions, &$status) {
            $statusForSurvey = $status[$survey->external_survey_company_id ? 'outsourced' : 'internal'];

            $surveyData = $survey->toArray();
            $surveyData['submission'] = collect($submissions)
                ->where('survey_id', $survey->id)
                ->first();

            if (!empty($surveyData['submission']['csr_submission_date']) && !empty($surveyData['schedule']['start'])) {
                $submissionDate = Carbon::createFromFormat('d/m/Y', $surveyData['submission']['csr_submission_date'])->startOfDay();
                $deadline = Carbon::parse($surveyData['schedule']['start'])->startOfDay();

                // Calculate the deadline with 14 days grace period
                $graceDeadline = $deadline->copy()->addDays(14);

                if ($submissionDate->isAfter($graceDeadline)) {
                    $statusForSurvey['failed']++;
                } else {
                    $statusForSurvey['passed']++;
                }
            } elseif (
                !empty($surveyData['external_survey_company'])
                || !empty($surveyData['surveyor_id'])
                || (!empty($surveyData['submission']['csr_status']) && $surveyData['submission']['csr_status'] == 'completed')
            ) {
                $statusForSurvey['inProgress']++;
            } else {
                $statusForSurvey['notStarted']++;
            }
            $status[$survey->external_survey_company_id ? 'outsourced' : 'internal'] = $statusForSurvey;
        });

        $responseData = [];
        foreach ($status as $type => $statuses) {
            $responseData[$type] = [
                [
                    'category' => 'Completed - SLA Passed',
                    'value' => $statuses['passed'],
                ],
                [
                    'category' => 'Completed - SLA Failed',
                    'value' => $statuses['failed'],
                ],
                [
                    'category' => 'In Progress',
                    'value' => $statuses['inProgress'],
                ],
                [
                    'category' => 'Not Started',
                    'value' => $statuses['notStarted'],
                ],
            ];
        }

        return response()->json($responseData);
    }

    public function getBoundData(Request $request)
    {
        $sectorId = $request->input('sectorId', '');
        $brokerId = $request->input('brokerId', '');
        $policyTypeId = $request->input('policyTypeId', '');
        $underwriterId = $request->input('underwriterId', '');
        $accountEngineerId = $request->input('accountEngineerId', '');
        [$startDate, $endDate] = $this->getDateRangeInput($request);
        $organisations = Organisation::withoutAppends()
            ->select('id')
            ->bound()
            ->when($sectorId, function ($query, $sectorId) {
                return $query->where('sector', $sectorId);
            })
            ->when($brokerId, function ($query, $brokerId) {
                return $query->where('broker_id', $brokerId);
            })
            ->when($policyTypeId, fn($query, $policyTypeId) =>
                $query->whereExists(function ($subquery) use ($policyTypeId) {
                    $subquery->select(DB::raw(1))
                        ->from('organisation_policy_numbers')
                        ->whereColumn('organisation_policy_numbers.organisation_id', 'organisations.id')
                        ->where('organisation_policy_numbers.policy_type_id', $policyTypeId)
                        ->whereNull('organisation_policy_numbers.deleted_at');
                })
            )
            ->when($underwriterId, function ($query, $underwriterId) {
                return $query->whereHas('contacts', function ($subquery) use ($underwriterId) {
                    $subquery->where('user_id', $underwriterId);
                });
            })
            ->when($accountEngineerId, function ($query, $accountEngineerId) {
                return $query->whereHas('contacts', function ($subquery) use ($accountEngineerId) {
                    $subquery->where('user_id', $accountEngineerId);
                });
            })
            ->get();

        $boundData = OrganisationPolicy::selectRaw('
                SUM(CASE WHEN bound_lead = 1 THEN 1 ELSE 0 END) as bound_lead,
                SUM(CASE WHEN bound_follow = 1 THEN 1 ELSE 0 END) as bound_follow,
                SUM(CASE WHEN bound_lead IS NULL AND bound_follow IS NULL THEN 1 ELSE 0 END) as didnt_bind
            ')
            ->whereIn('organisation_id', $organisations->pluck('id')->toArray())
            ->when($startDate, function ($query, $startDate) {
                return $query->where('created_at', '>=', $startDate);
            })
            ->when($endDate, function ($query, $endDate) {
                return $query->where('created_at', '<=', $endDate);
            })
            ->first()
            ->toArray();

        return response()->json($boundData);
    }

    private function getDateRangeInput(Request $request): array
    {
        $startDate = $request->input('startDate') ? Carbon::parse($request->input('startDate'))->endOfDay() : null;
        $endDate = $request->input('endDate') ? Carbon::parse($request->input('endDate'))->endOfDay() : null;
        return [$startDate, $endDate];
    }

    public function getOrganisationTableData(Request $request): JsonResponse
    {
        $sectorId = $request->input('sectorId', '');
        $brokerId = $request->input('brokerId', '');
        $policyTypeId = $request->input('policyTypeId', '');
        $underwriterId = $request->input('underwriterId', '');
        $accountEngineerId = $request->input('accountEngineerId', '');
        [$startDate, $endDate] = $this->getDateRangeInput($request);

        $organisations = Organisation::withoutAppends()
            ->when($sectorId, function ($query, $sectorId) {
                return $query->where('sector', $sectorId);
            })
            ->when($brokerId, function ($query, $brokerId) {
                return $query->where('broker_id', $brokerId);
            })
            ->when($policyTypeId, fn($query, $policyTypeId) =>
                $query->whereExists(function ($subquery) use ($policyTypeId) {
                    $subquery->select(DB::raw(1))
                        ->from('organisation_policy_numbers')
                        ->whereColumn('organisation_policy_numbers.organisation_id', 'organisations.id')
                        ->where('organisation_policy_numbers.policy_type_id', $policyTypeId)
                        ->whereNull('organisation_policy_numbers.deleted_at');
                })
            )
            ->when($underwriterId, function ($query, $underwriterId) {
                return $query->whereHas('contacts', function ($subquery) use ($underwriterId) {
                    $subquery->where('user_id', $underwriterId);
                });
            })
            ->when($accountEngineerId, function ($query, $accountEngineerId) {
                return $query->whereHas('contacts', function ($subquery) use ($accountEngineerId) {
                    $subquery->where('user_id', $accountEngineerId);
                });
            })
            ->when($startDate, function ($query, $startDate) {
                return $query->where('created_at', '>=', $startDate);
            })
            ->when($endDate, function ($query, $endDate) {
                return $query->where('created_at', '<=', $endDate);
            })
            ->orderBy('name', 'asc')
            ->get();

        try {
            foreach ($organisations as $org) {
                $propDoc = $org->propDoc;
                $org->lta = $propDoc ? $propDoc->longterm . ' Year/s' : 'No';
                $surveys = $org->surveys;
                $totalSurveys = $surveys->count();
                $surveyIds = $org->surveys()->pluck('id')->map(fn($id) => (string) $id);
                $completedSurveyCount = SurveyCards::whereIn('survey_id', $surveyIds)
                    ->where('broker-column', 'survey-completed')
                    ->count();
                $org->survey_completed_percentage = $totalSurveys > 0 ? (float)($completedSurveyCount ?? 0) / (float)$totalSurveys : 0;
                $org->desktop_survey_completed = $surveys->where('survey_type', 'dtr')->count() > 0;
                $org->survey_programme_in_place = $surveys->count() > 0;
                $org->completed_client_meetings = $this->getCompletedClientMeetingsByOrganisationId($org->id);
                $org->close_risk_rec = $this->getClosedRiskRecommendationsByOrganisationId($org->id);
            }
        } catch (\Exception $e) {
            \Log::error('Error fetching organisations: ' . $e->getMessage());
            return response()->json(['error' => 'An error occurred while fetching organisations.'], 500);
        }

        return response()->json($organisations);
    }

    private function getCompletedClientMeetingsByOrganisationId($organisationId)
    {
        $clientMeetings = ScheduleMeta::whereNull('deleted_at')
            ->where('key', 'status')
            ->where('value', 'completed')
            ->whereExists(function ($query) {
                $query->select(DB::raw(1))
                    ->from('schedule')
                    ->whereColumn('schedule.id', 'schedule_meta.schedule_id')
                    ->where('type', 're-admin')
                    ->whereNull('deleted_at');
            })
            ->whereExists(function ($query) use ($organisationId) {
                $query->select(DB::raw(1))
                    ->from('schedule_meta as org_check')
                    ->whereColumn('org_check.schedule_id', 'schedule_meta.schedule_id')
                    ->where('org_check.key', 'client_organisation_id')
                    ->where('org_check.value', $organisationId);
            })
            ->count();

        return $clientMeetings;
    }

    private function getClosedRiskRecommendationsByOrganisationId($organisationId)
    {
        $riskRecommendationCards = RiskRecommendationCards::raw(function ($collection) use ($organisationId) {
            return $collection->aggregate([
                [
                    '$match' => [
                        'organisation_id' => $organisationId
                    ]
                ],
                [
                    '$group' => [
                        '_id' => '$column',
                        'count' => ['$sum' => 1]
                    ]
                ],
                [
                    '$project' => [
                        'column' => '$_id',
                        'count' => 1,
                        '_id' => 0
                    ]
                ]
            ]);
        });
        $totalClosed = 0;
        $totalSubmissions = 0;
        foreach ($riskRecommendationCards as $item) {
            if ($item->column == 'closed') {
                $totalClosed = $item->count;
            }
            $totalSubmissions += $item->count;
        }
        return $totalSubmissions > 0 ? $totalClosed / $totalSubmissions : 0;
    }
}
