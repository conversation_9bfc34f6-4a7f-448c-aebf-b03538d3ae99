<?php

namespace App\Models;

use App\Models\AccessGroup\AccessGroupSector;
use Illuminate\Database\Eloquent\Model;
use <PERSON><PERSON><PERSON>\Mongodb\Eloquent\HybridRelations;
use Illuminate\Database\Eloquent\SoftDeletes as SoftDeletingTrait;

class Sector extends Model
{
    use SoftDeletingTrait, HybridRelations;

    protected $connection = 'mysql';

    /**
     * The database table used by the model.
     *
     * @var string
     */
    protected $table = 'sectors';

    /**
     * The attributes excluded from the model's JSON form.
     *
     * @var array
     */
    protected $hidden = [
        'pivot',
    ];

    protected $fillable = [
        'handle',
        'type',
        'created_at',
        'updated_at',
    ];

    public function accessGroupSector()
    {
        $this->hasOne(AccessGroupSector::class, 'rr_sector_id');
    }

    public function subSector()
    {
        return $this->hasMany(SubSector::class, 'sector_id', 'id');
    }
}
