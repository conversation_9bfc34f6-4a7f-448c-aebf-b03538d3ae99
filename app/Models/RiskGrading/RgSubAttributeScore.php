<?php

namespace App\Models\RiskGrading;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use App\Models\OrganisationLocations;
use App\Models\RGSubAttribute;

class RgSubAttributeScore extends Model
{
    use HasFactory;

    protected $fillable = [
        'organisation_location_id',
        'rg_sub_attribute_id',
        'score',
        'max_score',
        'risk_grading',
    ];

    public function organisationLocation()
    {
        return $this->belongsTo(OrganisationLocations::class, 'organisation_location_id', 'id');
    }

    public function subAttribute()
    {
        return $this->belongsTo(RGSubAttribute::class, 'rg_sub_attribute_id', 'id');
    }
}
