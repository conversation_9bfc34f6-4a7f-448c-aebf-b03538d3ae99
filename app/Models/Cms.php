<?php

namespace App\Models;

use GuzzleHttp\Client;
use GuzzleHttp\Exception\RequestException;

class Cms
{

    /**
     * Get method
     *
     * @return string
     * @var    string
     */
    public static function get($url)
    {
        // Create a new Guzzle client
        $client = new Client();
    
        try {
            // Make a GET request
            $response = $client->get(config('app.cms.endpoint') . $url, [
                'auth' => [config('app.cms.username'), config('app.cms.password')],
                'headers' => [
                    'X-LSMCMS-API-KEY' => config('app.cms.key'),
                ],
            ]);
    
            // Return the response body
            return $response->getBody()->getContents();
        } catch (RequestException $e) {
            // Handle exceptions
            return $e->getMessage();
        }
    }
}
