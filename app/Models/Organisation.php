<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use App\Models\OrganisationContact;
use App\Models\Sector;
use App\Models\SectorDocument;
use App\Models\OrganisationPropDoc;
use App\Models\OrganisationLocations;
use App\Models\Document;
use App\Models\MgaScheme;
use App\Models\User;
use App\Models\ClaimType;
use App\Models\CatiStat;
use App\Models\OrganisationProduct;
use App\Models\FileUpload;
use App\Models\Trade;
use App\Models\LibertyUser;
use App\Models\Broker;
use App\Models\OrganisationPolicy;
use App\Models\Branch;
use App\Models\OrganisationCover;
use App\Observers\HasDefaultFieldValue;
use App\Models\AccessGroup\AccessGroupOrganisation;
use App\Models\AccessGroup\AccessGroupPerson;
use Illuminate\Database\Eloquent\SoftDeletes as SoftDeletingTrait;
use App\Models\OrganisationLog;
use App\Models\Survey;

class Organisation extends Model
{
    use HasDefaultFieldValue, SoftDeletingTrait;

    protected $connection = 'mysql';
    /**
     * The database table used by the model.
     *
     * @var string
     */
    protected $table = 'organisations';

    protected $appends = [
        'users_count',
        'link',
    ];

    // This will be used to prevent this model from appending the users_count & link attribute
    // These two attribute causes slow query due to api request to aws and for counting all the users in org
    public static $withoutAppends = false;

    /**
     * The attributes excluded from the model's JSON form.
     *
     * @var array
     */
    protected $hidden = [];

    protected $fillable = [
        'rhs_no',
        'is_rhs',
        'name',
        'bound',
        'email',
        'phone',
        'address_line_1',
        'address_line_2',
        'postcode',
        'country',
        'sector',
        'status',
        'created_at',
        'updated_at',
        'logo',
        'description',
        'policy_number',
        'inception_date_of_cover',
        'expiry_date_of_cover',
        'safetymedia_url',
        'tpid',
        'liberty_branch',
        'mga_scheme',
        'trades_id',
        'dashboard_image',
        'theme',
        'loss_ratio',
        'bursary',
        'sov_cloudname',
        'sov_filename',
        'sov_filesize',
        'apply_claims',
        'risk_grading',
        'broker_id',
        'community_organisation',
        'product_sector',
        'product_subsector',
        'absence_case',
        'covid_19_wellbeing',
        'wellbeing_assessment',
        'has_previsico_access',
        'risk_score',
        'line_size',
        'premium',
        'position',
        'lead_insurer_id',
        'policy_segment',
        'sub_sector_id',
        'is_risk_insights', // temporary field for risk insights import
    ];

    protected function getArrayableAppends()
    {
        if (self::$withoutAppends) {
            return [];
        }
        return parent::getArrayableAppends();
    }

    /**
     * Default values for these fields will be set on 'saving' event if not set
     *
     * @return array
     */
    public function setDefaultValuesForFields(): array
    {
        return [
            'name'              => "",
            'phone'             => "",
            'address_line_1'    => "",
            'postcode'          => "",
            'country'           => "",
            'logo'              => "",
            'description'       => "",
            'trades_id'         => 0,
        ];
    }

    protected static function boot()
    {
        parent::boot();
    
        static::created(function ($organisation) {
            OrganisationLog::create([
                'organisation_id' => $organisation->id,
                // If bound is true, set bound_at, otherwise set pre_inception_at
                $organisation->bound ? 'bound_at' : 'pre_inception_at' => now(),
                'set_by' => request()->input('set_by'),
            ]);
        });
    
        static::updated(function ($organisation) {
            if ($organisation->wasChanged('bound')) {
                OrganisationLog::create([
                    'organisation_id' => $organisation->id,
                    // If bound is true, set bound_at, otherwise set pre_inception_at
                    $organisation->bound ? 'bound_at' : 'pre_inception_at' => now(),
                    'set_by' => request()->input('set_by'),
                ]);
            }
        });
    }

    public function leadInsurer()
    {
        return $this->belongsTo(LeadInsurer::class, 'lead_insurer_id', 'id');
    }

    public function products()
    {
        return $this->hasMany(OrganisationProduct::class);
    }

    public function setLinkAttribute($value)
    {
        return $this->attributes['link'] = $value;
    }

    public function getLinkAttribute()
    {
        $fileUpload = new FileUpload();
        if ($this->logo) {
            return $fileUpload->link($this->logo, '7 days');
        }

        return null;
    }

    public function propDoc()
    {
        return $this->hasOne(OrganisationPropDoc::class);
    }

    public function claims()
    {
        return $this->belongsToMany(ClaimType::class)->withTimestamps();
    }

    public function contacts()
    {
        return $this->hasMany(OrganisationContact::class, 'organisation_id', 'id');
    }

    public function orgRiskEngineer()
    {
        return $this
            ->hasMany(OrganisationContact::class, 'organisation_id', 'id')
            ->where('type', 'risk-engineer');
    }

    public function orgUnderwriter()
    {
        return $this
            ->hasMany(OrganisationContact::class, 'organisation_id', 'id')
            ->where('type', 'underwriter');
    }
    /**
     * Get all of the locations for the Organisation
     *
     * @return HasMany
     */
    public function locations()
    {
        return $this->hasMany(OrganisationLocations::class, 'organisation_id', 'id');
    }

    /**
     * Get all of the surveys for the Organisation
     *
     * @return HasMany
     */
    public function surveys()
    {
        return $this->hasMany(Survey::class, 'organisation_id', 'id');
    }

    public function getUsersCountAttribute()
    {
        return $this->users()->count();
    }

    /**
     * User Relationship
     */
    public function users()
    {
        return $this
            ->hasMany(User::class)
            ->where('users.secondary_contact', 0);
    }

    /**
     * User Relationship
     */
    public function secondaryContacts()
    {
        $secondary_contact_ids = $this
            ->hasMany(OrganisationContact::class)
            ->where('type', 'secondary-client-contact')
            ->pluck('user_id');

        return $this->hasMany(User::class)->whereIn('id', $secondary_contact_ids);
    }

    /**
     * Trade Relationship
     */
    public function trade()
    {
        return $this->hasOne(Trade::class, 'id', 'trades_id');
    }

    /**
     * Trade Relationship
     */
    public function mgascheme()
    {
        return $this->hasOne(MgaScheme::class, 'id', 'mga_scheme');
    }

    public function userRoles()
    {
        return LibertyUser::where('branch_id', '=', $this->liberty_branch)
            ->whereIn('role', ['underwriter', 'risk-engineer'])->get();
    }

    public function adminUsers()
    {
        return LibertyUser::where('role', 'admin')->get();
    }


    public function brokers()
    {
        return isset($this->mgascheme->broker_id)
            ? Broker::whereIn('id', [$this->mgascheme->broker_id])->get()
            : [];
    }

    /**
     * Document Relationship
     */
    public function documents()
    {
        $documents = $this->hasMany(Document::class);
        return $documents;
    }

    public function policies()
    {
        return $this->hasMany(OrganisationPolicy::class, 'organisation_id', 'id');
    }

    // Get Org active policy within the 30 days grace period
    public function activePolicies()
    {
        return $this->hasMany(OrganisationPolicy::class, 'organisation_id', 'id')
            ->where('organisation_policy_numbers.expiry_date_of_cover', '>=', now()->startOfDay()->subDays(30));
    }

    public function policyNumbers()
    {
        $policies = OrganisationPolicy::leftJoin(
            'policy_document',
            function ($join) {
                $join->on('organisation_policy_numbers.policy_type_id', '=', 'policy_document.document_type_id');
                $join->on('organisation_policy_numbers.organisation_id', '=', 'policy_document.organisation_id');
                $join->on('organisation_policy_numbers.deleted_at', '=', 'policy_document.deleted_at');
            }
        )
            ->where('organisation_policy_numbers.organisation_id', '=', $this->id)
            ->where('organisation_policy_numbers.deleted_at', '=', null)
            //->where('policy_document.deleted_at', '=', null)
            ->groupBy('organisation_policy_numbers.id')
            ->orderBy('policy_document.id', 'DESC')
            ->get();

        foreach ($policies as $key => $policy) {
            $policies[$key]->type = $policy->type;
        }

        return $policies;
    }

    public function policyUpdatedNumbers()
    {
        $policies = OrganisationPolicy::leftJoin(
            'policy_document',
            function ($join) {
                $join->on('organisation_policy_numbers.policy_type_id', '=', 'policy_document.document_type_id');
                $join->on('organisation_policy_numbers.organisation_id', '=', 'policy_document.organisation_id');
            }
        )
            ->where('organisation_policy_numbers.organisation_id', '=', $this->id)
            ->where('organisation_policy_numbers.deleted_at', '=', null)
            //->groupBy('organisation_policy_numbers.id')
            ->orderBy('policy_document.id','ASC')
            ->get();

        foreach ($policies as $key => $policy) {
            $policies[$key]->type = $policy->type;
        }

        return $policies;
    }

    public function safetyMediaAccess()
    {
        $users = $this->hasMany(User::class)->where('safetymedia_access', 1)->first();
        return isset($users);
    }


    public function sectorDocuments()
    {
        return $this->hasMany(SectorDocument::class, 'sector_id', 'sector');
    }

    public function branch()
    {
        return $this->hasOne(Branch::class, 'id', 'liberty_branch');
    }

    public function libertyBranch()
    {
        $branch = Branch::where('id', '=', $this->liberty_branch)->first();
        return $branch;
    }

    public function sector()
    {
        return $this->hasOne(Sector::class, 'id', 'sector');
    }

    public function subSector()
    {
        return $this->hasOne(SubSector::class, 'id', 'sub_sector_id');
    }

    // public function sectorName()
    // {
    //     $sector
    //     return $this;
    // }

    /**
     * Sector Relationship
     */

    public function organisationCovers()
    {
        $covers = [];
        $pivot = $this->hasMany(OrganisationCover::class, 'organisation_id', 'id')->get();
        foreach ($pivot as $cov) {
            //print_r($cov->covertype);exit;
            $covers[] = $cov->covertype;
        }
        //print_r($covers);exit;
        return $covers;
    }

    public function catiStatistics()
    {
        return $this->hasOne(CatiStat::class, 'client_id', 'cati_id');
    }

    public function broker()
    {
        return $this->belongsTo(Broker::class);
    }

    public function broker_organisations()
    {
        return $this->belongsTo(Broker::class, 'broker_id', 'id');
    }

    public function assign_brokers()
    {
        return $this
            ->hasMany(OrganisationContact::class, 'organisation_id', 'id')
            ->where('type', 'broker-user-contact');
    }

    public function scopeHasCommunityAccess($query)
    {
        return $query->where('community_organisation', 1);
    }

    public function organisationSectionSettings()
    {
        return $this->hasOne(OrganisationSectionSetting::class, 'organisation_id', 'id');
    }

    public function accessGroupOrganisation()
    {
        return $this->hasOne(AccessGroupOrganisation::class, 'rr_organisation_id', 'id');
    }

    public function accessGroupPerson()
    {
        return $this->hasMany(AccessGroupPerson::class, 'rr_organisation_id', 'id');
    }

    public function PrevisicoNotificationUsers($orgLocationId=null)
    {
        $users=$this->hasMany(User::class, 'organisation_id', 'id');
        $users
            ->where('users.organisation_id', $this->id)
            ->where('users.secondary_contact', 0)
            ->where('manager', 1)
            ->where('activated', 1)
            ->where('has_previsico_access', 1)
            ->join('previsico_asset_access', 'previsico_asset_access.user_id', '=', 'users.id')
            ->join('previsico_assets', 'previsico_assets.id', '=', 'previsico_asset_access.previsico_asset_id');
            if($orgLocationId > 0){
                $users->where('previsico_assets.organisation_location_id', $orgLocationId);
            }
            return $users->get(['users.id','users.email']);
    }

    public static function withoutAppends()
    {
        $org = new Organisation();
        $org::$withoutAppends = true;
        return $org;
    }

    public function scopeBound($query)
    {
        return $query->where('bound', 1);
    }
}
