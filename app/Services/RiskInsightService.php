<?php

namespace App\Services;

use App\Models\LibertyUser;
use App\Models\Mailqueue;
use App\Models\Organisation;
use App\Models\RiskInsights\RiskInsightAuditLogs;
use App\Models\RiskInsights\RiskInsightFinalAuditLogs;
use App\Models\RiskInsights\DocumentRequest;
use App\Services\RAFAService;

class RiskInsightService
{
    public function __construct()
    {

    }

    public function getRiskReportDataByDocumentId($documentId)
    {
        // ToDo: get by documentId
        $document = DocumentRequest::with('assignedUser')->where('id', (int)$documentId)->first();
        $auditLogs = RiskInsightAuditLogs::where('document_id', (int)$documentId)->orderBy('version', 'desc')->first();
        // $rafaService = new RAFAService();
        // // TODO: remove this and fetch the latest audit log
        // $auditLogs = $rafaService->getDetailedResult('1', '1');
        return (object)[
            'document' => $document,
            'auditLogs' => $auditLogs,
        ];
    }

    public function getFinalRiskReportDataByDocumentId($documentId)
    {
        $document = DocumentRequest::first();
        $auditLogs = RiskInsightFinalAuditLogs::where('document_id', $documentId)->first();
        return (object)[
            'document' => $document,
            'auditLogs' => $auditLogs,
        ];
    }

    public function saveRAFAResult($documentId, $updatedBy, $fileLocationData, $locationIds, $orgId, $isNHITL = false)
    {
        $rafaService = new RAFAService();
        $rafaData = $rafaService->getDetailedResult('1', '1');

        if (!is_array($rafaData) && !is_object($rafaData)) {
            throw new \Exception('RAFA data must be an array or object');
        }

        // Convert string parameters to integers
        $documentId = (int)$documentId;
        $updatedBy = (int)$updatedBy;
        $rafaData = '{"client_name":"Tesco Plc","sector":"Retail","subsector":"Food & Beverage","request_id":"123e4567-e89b-12d3-a456-426614174000","locations":[{"document_id":"a1b2c3d4-e5f6-7890-abcd-ef1234567890","document_name":"47c1ce88-b536-413f-a888-34d4dc762105.pdf","broker_name":"Aon","location":"Danes Way","postcode":"NN6 7GX","surveyor_name":"John Doe","surveyor_organisation":"External surveyors organisation","executive_summary":"Lorem ipsum dolor sit amet, consectetur adipiscing elit. Nullam auctor, nunc id aliquam tincidunt, nisi nunc tincidunt urna, nec tincidunt nunc nunc id nunc.","underwriter":"John Doe","survey_date":"2024-05-12","report_level_grading":{"max_score":50,"score":28.75,"percentage":"58%","rating":"Below Average"},"risk_grading":{"construction_and_exposures":{"max_score":50,"score":28.75,"percentage":"58%","details":{"materials_of_construction":{"max_score":30,"rating":"Average","score":15,"commentaries":[{"commentary":"No third party exposures with good management of first party exposures, such as idle pallets","reference":{"page":[10]}}]},"compartmentalisation":{"max_score":15,"rating":"Above Average","score":11.25,"commentaries":[{"commentary":"Effective fire compartmentation between major areas of the building","reference":{"page":[12]}}]},"exposures":{"max_score":5,"rating":"Average","score":2.5,"commentaries":[{"commentary":"Some minor exposures from neighboring properties, but generally well-managed","reference":{"page":[15]}}]}}},"fire_protection_and_detection":{"max_score":116,"score":37.5,"percentage":"32%","details":{"sprinkler_protection":{"max_score":90,"rating":"Below Average","score":22.5,"commentaries":[{"commentary":"Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.","reference":{"page":[20]}}]},"automatic_fire_detection":{"max_score":8,"rating":"Average","score":4,"commentaries":[{"commentary":"Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat.","reference":{"page":[21]}}]},"fire_brigade_and_water_supply":{"max_score":8,"rating":"Above Average","score":6,"commentaries":[{"commentary":"Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur.","reference":{"page":[22]}}]},"fire_extinguishers":{"max_score":2,"rating":"Average","score":1,"commentaries":[{"commentary":"Excepteur sint occaecat cupidatat non proident, sunt in culpa qui officia deserunt mollit anim id est laborum.","reference":{"page":[23]}}]},"special_protection":{"max_score":8,"rating":"Average","score":4,"commentaries":[{"commentary":"Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.","reference":{"page":[24]}}]}}},"management_systems":{"max_score":31,"score":14.25,"percentage":"46%","details":{"housekeeping":{"max_score":2,"rating":"Below Average","score":0.5,"commentaries":[{"commentary":"Lorem ipsum dolor sit amet, consectetur adipiscing elit.","reference":{"page":[1]}}]},"hot_work_controls":{"max_score":5,"rating":"Average","score":2.5,"commentaries":[{"commentary":"Lorem ipsum dolor sit amet, consectetur adipiscing elit.","reference":{"page":[2]}}]},"smoking_controls":{"max_score":2,"rating":"Average","score":1,"commentaries":[{"commentary":"Lorem ipsum dolor sit amet, consectetur adipiscing elit.","reference":{"page":[3]}}]},"electrical_maintenance":{"max_score":3,"rating":"Average","score":1.5,"commentaries":[{"commentary":"Lorem ipsum dolor sit amet, consectetur adipiscing elit.","reference":{"page":4}}]},"contractor_controls":{"max_score":3,"rating":"Average","score":1.5,"commentaries":[{"commentary":"Lorem ipsum dolor sit amet, consectetur adipiscing elit.","reference":{"page":5}}]},"auditing":{"max_score":5,"rating":"Average","score":2.5,"commentaries":[{"commentary":"Lorem ipsum dolor sit amet, consectetur adipiscing elit.","reference":{"page":6}}]},"incident_planning":{"max_score":2,"rating":"Average","score":1,"commentaries":[{"commentary":"Lorem ipsum dolor sit amet, consectetur adipiscing elit.","reference":{"page":7}}]},"mechanical_maintenance":{"max_score":3,"rating":"Average","score":1.5,"commentaries":[{"commentary":"Lorem ipsum dolor sit amet, consectetur adipiscing elit.","reference":{"page":8}}]},"modification_controls":{"max_score":3,"rating":"Below Average","score":0.75,"commentaries":[{"commentary":"Lorem ipsum dolor sit amet, consectetur adipiscing elit.","reference":{"page":9}}]},"training":{"max_score":3,"rating":"Average","score":1.5,"commentaries":[{"commentary":"Lorem ipsum dolor sit amet, consectetur adipiscing elit.","reference":{"page":10}}]}}},"security":{"max_score":18,"score":16,"percentage":"89%","details":{"access_control":{"max_score":4,"rating":"Above Average","score":3,"commentaries":[{"commentary":"Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.","reference":{"page":1}}]},"intruder_alarm_and_cctv":{"max_score":5,"rating":"Superior","score":5,"commentaries":[{"commentary":"Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat.","reference":{"page":2}}]},"perimeter_fencing_and_lighting":{"max_score":4,"rating":"Above Average","score":3,"commentaries":[{"commentary":"Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur.","reference":{"page":3}}]},"security_guarding":{"max_score":5,"rating":"Superior","score":5,"commentaries":[{"commentary":"Excepteur sint occaecat cupidatat non proident, sunt in culpa qui officia deserunt mollit anim id est laborum.","reference":{"page":4}}]}}},"hazards":{"max_score":50,"score":25,"percentage":"50%","details":{"burning_load":{"max_score":10,"rating":"Average","score":5,"commentaries":[{"commentary":"Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.","reference":{"page":42}}]},"flammable_liquids_solids_gases":{"max_score":10,"rating":"Average","score":5,"commentaries":[{"commentary":"Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat.","reference":{"page":45}}]},"explosive_gases_and_dusts":{"max_score":4,"rating":"Average","score":2,"commentaries":[{"commentary":"Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur.","reference":{"page":48}}]},"process_control_systems":{"max_score":4,"rating":"Average","score":2,"commentaries":[{"commentary":"Excepteur sint occaecat cupidatat non proident, sunt in culpa qui officia deserunt mollit anim id est laborum.","reference":{"page":51}}]},"other_special_hazards":{"max_score":22,"rating":"Average","score":11,"commentaries":[{"commentary":"Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.","reference":{"page":54}}]}}},"utilities":{"max_score":6,"score":6,"percentage":"100%","details":{"supply":{"max_score":6,"rating":"Superior","score":6,"commentaries":[{"commentary":"Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.","reference":{"page":55}}]}}},"special_perils":{"max_score":16,"score":8,"percentage":"50%","details":{"malicious_damage":{"max_score":2,"rating":"Average","score":1,"commentaries":[{"commentary":"Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.","reference":{"page":65}}]},"earthquake":{"max_score":2,"rating":"Average","score":1,"commentaries":[{"commentary":"Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat.","reference":{"page":68}}]},"flooding":{"max_score":10,"rating":"Average","score":5,"commentaries":[{"commentary":"Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur.","reference":{"page":70}}]},"windstorm":{"max_score":2,"rating":"Average","score":1,"commentaries":[{"commentary":"Excepteur sint occaecat cupidatat non proident, sunt in culpa qui officia deserunt mollit anim id est laborum.","reference":{"page":73}}]}}},"financial_and_business_risk":{"max_score":31,"percentage":"33%","score":10.25,"details":{"bcp":{"max_score":25,"rating":"Below Average","score":6.25,"commentaries":[{"commentary":"Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.","reference":{"page":1}}]},"seasonal_influences":{"max_score":2,"rating":"Above Average","score":1.5,"commentaries":[{"commentary":"Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat.","reference":{"page":2}}]},"critical_plant_and_equipment":{"max_score":2,"rating":"Average","score":1,"commentaries":[{"commentary":"Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur.","reference":{"page":3}}]},"key_customers_and_suppliers":{"max_score":2,"rating":"Above Average","score":1.5,"commentaries":[{"commentary":"Excepteur sint occaecat cupidatat non proident, sunt in culpa qui officia deserunt mollit anim id est laborum.","reference":{"page":4}}]}}}}}]}';
        $rafaData = json_decode($rafaData);
        RiskInsightAuditLogs::create([
            'document_id' => (int)$documentId,
            'metadata_update' => $rafaData,
            'selected_location_ids' => $locationIds,
            'mapped_locations' => $this->calculateLocationWeightage($orgId, $fileLocationData),
            'updated_by' => $updatedBy,
            'version' => 1,
        ]);

        DocumentRequest::where('id', $documentId)->update([
            'status' => $isNHITL ? DocumentRequest::STATUS_COMPLETED : DocumentRequest::STATUS_OPEN
        ]);

        return $rafaData;

    }

    /**
     * Calculate the location weightage based on location TIV and overall TIV
     *
     * @param int $orgId
     * @param array $fileLocationData
     * @return array
     */
    private function calculateLocationWeightage($orgId, $fileLocationData)
    {
        $mappedSelectedLocationWithFiles = [];
        $overallTiv = 0;
        foreach ($fileLocationData as $fileLocation) {
            $mappedSelectedLocationWithFiles[$fileLocation['locationId']] = [
                'organisation_id' => $orgId,
                'location'        => $fileLocation['locationName'],
                'tiv'             => $fileLocation['locationTiv'],
                'weightage'       => 0,
            ];

            $overallTiv += (int)$fileLocation['locationTiv'];
        }

        // If overall TIV is 0, set equal weightage for all locations
        if ($overallTiv === 0) {
            $equalWeightage = count($mappedSelectedLocationWithFiles) > 0 ? round(100 / count($mappedSelectedLocationWithFiles), 2) : 0;
            foreach ($mappedSelectedLocationWithFiles as $key => $value) {
                $mappedSelectedLocationWithFiles[$key]['weightage'] = $equalWeightage;
            }
        } else {
            foreach ($mappedSelectedLocationWithFiles as $key => $value) {
                $mappedSelectedLocationWithFiles[$key]['weightage'] = round($value['tiv'] / $overallTiv * 100, 2);
            }
        }

        return $mappedSelectedLocationWithFiles;
    }

    public function updateRiskReport($documentId, $metadata, $updatedBy)
    {
        $version = (RiskInsightAuditLogs::where('document_id', (int)$documentId)->max('version') ?? 0) + 1;
        RiskInsightAuditLogs::create([
            'document_id' => (int)$documentId,
            'metadata_update' => $metadata,
            'updated_by' => $updatedBy,
            'version' => $version,
        ]);
    }

    public function finalizeRiskReport($documentId)
    {
        $document = DocumentRequest::where('id', (int)$documentId)->first();
        $latestAuditLog = RiskInsightAuditLogs::where('document_id', (int)$documentId)->orderBy('version', 'desc')->first();
        RiskInsightFinalAuditLogs::create([
            'document_id' => (int)$documentId,
            'metadata' => $latestAuditLog->metadata_update,
            'finalized_by' => $document->assigned_to_user,
        ]);

        $document = DocumentRequest::where('id', (int)$documentId)->update([
            'is_reviewed' => 1,
        ]);

        return $document;
    }

    public function createDocument($documentId, $metadata, $updatedBy)
    {
        RiskInsightDocument::create([
            'document_id' => $documentId,
            'metadata' => $metadata,
        ]);
        RiskInsightAuditLogs::create([
            'document_id' => $documentId,
            'metadata_update' => $metadata,
        ]);
    }

    public function getLatestAuditLog($documentId)
    {
        $auditLog = new RiskInsightAuditLogs();
        $latestVersion = $auditLog->getLatestVersion($documentId);
        return $latestVersion;
    }

    public function lockRiskReportToUser($documentId, $userId)
    {
        $document = DocumentRequest::where('id', (int)$documentId)->first();
        $document->assigned_to_user = $userId;
        $document->status = DocumentRequest::STATUS_UNDER_REVIEW;
        $document->save();
        return $document;
    }

    public function updateDocumentStatus($document, $user)
    {
        if ($document->do_skip_human_in_the_loop) {
            $document->status = DocumentRequest::STATUS_COMPLETED;
            $document->save();
            return $document;
        }

        $status = $document->status;
        $isUnderwriter = $user->role === 'underwriter';
        $isRiskEngineer = $user->role === 'risk-engineer';

        switch ($status) {
            case DocumentRequest::STATUS_OPEN:
                if($isUnderwriter
                    || ($isRiskEngineer && !$document->disable_risk_engineer_evaluation)
                ) {
                    $document->assigned_to_user = $user->id;
                    $document->status = DocumentRequest::STATUS_UNDER_REVIEW;
                    $document->save();
                }
                return $document;
            case DocumentRequest::STATUS_REVIEWED_BY_RE:
                if($isUnderwriter) {
                    $document->assigned_to_user = $user->id;
                    $document->status = DocumentRequest::STATUS_UNDER_REVIEW;
                    $document->save();
                }
                return $document;
            case DocumentRequest::STATUS_UNDER_REVIEW:
                $document->status = $isRiskEngineer ? DocumentRequest::STATUS_REVIEWED_BY_RE : DocumentRequest::STATUS_COMPLETED;
                $document->assigned_to_user = $isRiskEngineer ? null : $document->assigned_to_user;
                // finalize or add to database
                $document->save();

                if ($isRiskEngineer) {
                    // send email to UWs
                    $uwEmails = $this->extractUW($document->organisation_id);
                    $this->sendNotification($uwEmails);
                }
                return $document;
            default:
                return $document;
        }
    }

    private function extractUW($orgId): array
    {
        $underwriter = Organisation::where('id', $orgId)->first()->orgUnderwriter;

        $uwEmails = [];

        $uwIds = [];
        foreach ($underwriter as $uw) {
            $uwIds[] = $uw->user_id;
        }

        if (count($uwIds) > 0) {
            $underwriters = LibertyUser::whereIn('id', $uwIds)->get(['first_name', 'last_name', 'email']);
            foreach ($underwriters as $underwriter) {
                $fullName = trim($underwriter->first_name . ' ' . $underwriter->last_name);
                $uwEmails[$fullName] = $underwriter->email;
            }
        }

        return $uwEmails;
    }

    private function sendNotification(array $emails): void
    {
        foreach ($emails as $fullName => $email) {
            try {
                (new Mailqueue())->queue(
                    $email,
                    $fullName,
                    'Risk Insight Document Notification',
                    'emails.risk-insight.document-reviewed', 
                    [
                        'fullName' => $fullName,
                        'documentLink' => '',
                    ]
                );
            } catch (\Exception $e) {
                \Log::error('Error sending notification to ' . $fullName . ': ' . $e->getMessage());
            }
        }
    }

    public function removeDocumentNHITL($documentId)
    {
        $document = DocumentRequest::where('id', (int)$documentId)->first();
        $document->do_skip_human_in_the_loop = 0;
        $document->status = DocumentRequest::STATUS_OPEN;
        $document->save();
        return $document;
    }
}
