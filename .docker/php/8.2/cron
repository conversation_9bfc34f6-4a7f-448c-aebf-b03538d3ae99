#*/1 * * * * cd /var/www/html; /usr/local/bin/php artisan virtual-rooms:last-call-activity-checker > /dev/null 2>&1
#*/5 08-18 * * 1-5 cd /var/www/html; /usr/local/bin/php artisan virtual-rooms:before-meeting-reminder > /dev/null 2>&1
0 8 * * * cd /var/www/html; /usr/local/bin/php artisan notify:broker-chaser-notifications > /dev/null 2>&1
#00 23 * * * cd /var/www/html; /usr/local/bin/php artisan riskreduce:cati_import > /dev/null 2>&1
#0 8 1 * * cd /var/www/html; /usr/local/bin/php artisan notify:client-monthly-notification > /dev/null 2>&1
#0 10 * * * cd /var/www/html; /usr/local/bin/php artisan riskreduce:community-summary --summary=daily > /dev/null 2>&1
10 1 * * * cd /var/www/html; /usr/local/bin/php artisan riskreduce:email-form-submission-report formyPublic > /dev/null 2>&1
#* * 1 * * cd /var/www/html; /usr/local/bin/php artisan claims-client-dashboard:fetch-data-demo-org > /dev/null 2>&1
0 0 * * 1-5 cd /var/www/html; /usr/local/bin/php artisan claims-client-dashboard:fetch-data > /dev/null 2>&1
0 3 * * 1-5 /var/www/html; /usr/local/bin/php artisan recache:client-claims-perma-cache > /dev/null 2>&1
#0 * * * * cd /var/www/html; /usr/local/bin/php artisan riskreduce:import-claims --truncate > /dev/null 2>&1
* * * * * cd /var/www/html; /usr/local/bin/php artisan previsico:send-alert > /dev/null 2>&1
0 0 * * * cd /var/www/html; /usr/local/bin/php artisan previsico:generate_assets > /dev/null 2>&1
# */15 * * * * cd /var/www/html; /usr/local/bin/php artisan previsico:data-fetch > /dev/null 2>&1
* * * * * cd /var/www/html; /usr/local/bin/php artisan rackspace:claim > /dev/null 2>&1
*/15 * * * * cd /var/www/html; /usr/local/bin/php artisan riskreduce:riskrec > /dev/null 2>&1
01 02 * * * cd /var/www/html; /usr/local/bin/php artisan riskreduce:cleanup > /dev/null 2>&1
05 02 * * * cd /var/www/html; /usr/local/bin/php artisan riskreduce:send-rereview-reminder > /dev/null 2>&1
00 02 * * * cd /var/www/html; /usr/local/bin/php artisan riskreduce:send-survey-reminder > /dev/null 2>&1
#* * * * * cd /var/www/html; /usr/local/bin/php artisan virtual-rooms:sms-before-meeting-reminder > /dev/null 2>&1
#30 23 * * * cd /var/www/html; /usr/local/bin/php artisan riskreduce:suspend_cati_organisation > /dev/null 2>&1
#*/5 * * * * cd /var/www/html; /usr/local/bin/php artisan virtual-rooms:sync-announcement-categories > /dev/null 2>&1
0 0 1 * * cd /var/www/html; /usr/local/bin/php artisan portfolioviews:scrape > /dev/null 2>&1
# 0 0 * * * cd /var/www/html; /usr/local/bin/php artisan access-control:annual-password-reset > /dev/null 2>&1
0 0 * * * cd /var/www/html; /usr/local/bin/php artisan riskreduce:delete-email-logs > /dev/null 2>&1
0 0 * * * cd /var/www/html; /usr/local/bin/php artisan riskreduce:auto-approve-csr > /dev/null 2>&1
0 0 * * * cd /var/www/html; /usr/local/bin/php artisan sync:chaser-notification > /dev/null 2>&1
*/15 * * * * cd /var/www/html; /usr/local/bin/php artisan previsico:clear_floodmap_station_data > /dev/null 2>&1
* * * * * cd /var/www/html; /usr/local/bin/php artisan riskreduce:sync-riskrec-cards-kanban > /dev/null 2>&1
* * * * * cd /var/www/html; /usr/local/bin/php artisan surveys:export > /dev/null 2>&1
* * * * * cd /var/www/html; /usr/local/bin/php artisan risk-rec:export > /dev/null 2>&1
0 0 1 * * cd /var/www/html; /usr/local/bin/php artisan log:re-metrics > /dev/null 2>&1
* * * * * cd /var/www/html; /usr/local/bin/php artisan risk-insight:check-document-changes > /dev/null 2>&1